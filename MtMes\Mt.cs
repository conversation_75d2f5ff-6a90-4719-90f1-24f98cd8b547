﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using MtMes.Enums;
using MtMes.Models;
using Newtonsoft.Json;

namespace MtMes
{
    public static class Mt
    {
        #region 全局变量属性

        private static string Host { get; set; } = "https://inf-openapi.apigw.test.meituan.com";
        private static string SCaApp { get; set; } = "isv_boshijie";
        private static string Secret { get; set; } = "vZEudb70qumn853uxEtJ0oyyyP9oO6Kl";

        // 
        public static string BizLineCode { get; set; } = "QXDC"; // 电单车业务传：QXDDC，单车业务传：QXDC
        public static string SupplierCode { get; set; } = "10209799";
        public static string FactoryCode { get; set; } = "QXDC-10209799";

        #endregion

        #region 公共方法

        /// <summary>
        /// 上传数据
        /// </summary>
        public static MtResult UploadTestDetail(string batchId, List<TestItem> dataList)
        {
            try
            {
                // 构建请求体
                TestDetail detail = new TestDetail
                {
                    templateId = EnumTemplate.过程管理数据,
                    bizLineCode = BizLineCode,
                    supplierCode = SupplierCode,
                    factoryCode = FactoryCode,
                    batchId = batchId,
                    data = dataList
                };

                // 计算Content-MD5
                string requestBody = JsonConvert.SerializeObject(detail);
                string contentMd5 = GetMd5(requestBody);

                // 构造签名字符串计算sha256签名
                string httpMethod = "POST";
                string urlPath = "/api/eip/record/upload";
                string stringToSign = $"{httpMethod}\n{contentMd5}\n{urlPath}";
                string sign = GetSha256(stringToSign);

                // 构建提交
                string responseContent;
                using (HttpClient httpClient = new HttpClient())
                {
                    httpClient.BaseAddress = new Uri(Host);
                    httpClient.Timeout = TimeSpan.FromSeconds(10);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Signature", sign);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Timestamp", DateTimeOffset.Now.ToUnixTimeMilliseconds().ToString());
                    httpClient.DefaultRequestHeaders.Add("S-Ca-App", SCaApp);
                    StringContent content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                    content.Headers.Add("Content-MD5", contentMd5);

                    // 同步调用（网络不好时会阻塞线程）
                    HttpResponseMessage response = httpClient.PostAsync("/api/eip/record/upload", content).Result;
                    response.EnsureSuccessStatusCode();
                    responseContent = response.Content.ReadAsStringAsync().Result;
                }

                // 判断返回值
                MtResponse mtResponse = JsonConvert.DeserializeObject<MtResponse>(responseContent);
                return new MtResult
                {
                    Success = mtResponse.code.Equals("00000"),
                    Result = responseContent
                };
            }
            catch (Exception ex)
            {
                return new MtResult
                {
                    Success = true,
                    Result = ex.ToString()
                };
            }
        }

        /// <summary>
        /// 上传数据
        /// </summary>
        public static MtResult UploadMultiCodeDetail(string batchId, List<MultiCodeItem> dataList)
        {
            try
            {
                // 构建请求体
                MultiCodeDetail detail = new MultiCodeDetail
                {
                    templateId = EnumTemplate.多码合一明细数据,
                    bizLineCode = BizLineCode,
                    supplierCode = SupplierCode,
                    factoryCode = FactoryCode,
                    batchId = batchId,
                    data = dataList
                };

                // 计算Content-MD5
                string requestBody = JsonConvert.SerializeObject(detail);
                string contentMd5 = GetMd5(requestBody);

                // 构造签名字符串计算sha256签名
                string httpMethod = "POST";
                string urlPath = "/api/eip/record/upload";
                string stringToSign = $"{httpMethod}\n{contentMd5}\n{urlPath}";
                string sign = GetSha256(stringToSign);

                // 构建提交
                string responseContent;
                using (HttpClient httpClient = new HttpClient())
                {
                    httpClient.BaseAddress = new Uri(Host);
                    httpClient.Timeout = TimeSpan.FromSeconds(10);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Signature", sign);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Timestamp", DateTimeOffset.Now.ToUnixTimeMilliseconds().ToString());
                    httpClient.DefaultRequestHeaders.Add("S-Ca-App", SCaApp);
                    StringContent content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                    content.Headers.Add("Content-MD5", contentMd5);

                    // 同步调用（网络不好时会阻塞线程）
                    HttpResponseMessage response = httpClient.PostAsync("/api/eip/record/upload", content).Result;
                    response.EnsureSuccessStatusCode();
                    responseContent = response.Content.ReadAsStringAsync().Result;
                }

                // 判断返回值
                MtResponse mtResponse = JsonConvert.DeserializeObject<MtResponse>(responseContent);
                return new MtResult
                {
                    Success = mtResponse.code.Equals("00000"),
                    Result = responseContent
                };
            }
            catch (Exception ex)
            {
                return new MtResult
                {
                    Success = true,
                    Result = ex.ToString()
                };
            }
        }

        /// <summary>
        /// 上传数据
        /// </summary>
        public static MtResult UploadTraceCodeBindDetail(string batchId, List<TraceCodeItem> dataList)
        {
            try
            {
                // 构建请求体
                TraceCodeBindDetail detail = new TraceCodeBindDetail
                {
                    templateId = EnumTemplate.追溯件绑定数据模板,
                    bizLineCode = BizLineCode,
                    supplierCode = SupplierCode,
                    factoryCode = FactoryCode,
                    batchId = batchId,
                    data = dataList
                };

                // 计算Content-MD5
                string requestBody = JsonConvert.SerializeObject(detail);
                string contentMd5 = GetMd5(requestBody);

                // 构造签名字符串计算sha256签名
                string httpMethod = "POST";
                string urlPath = "/api/eip/record/upload";
                string stringToSign = $"{httpMethod}\n{contentMd5}\n{urlPath}";
                string sign = GetSha256(stringToSign);

                // 构建提交
                string responseContent;
                using (HttpClient httpClient = new HttpClient())
                {
                    httpClient.BaseAddress = new Uri(Host);
                    httpClient.Timeout = TimeSpan.FromSeconds(10);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Signature", sign);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Timestamp", DateTimeOffset.Now.ToUnixTimeMilliseconds().ToString());
                    httpClient.DefaultRequestHeaders.Add("S-Ca-App", SCaApp);
                    StringContent content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                    content.Headers.Add("Content-MD5", contentMd5);

                    // 同步调用（网络不好时会阻塞线程）
                    HttpResponseMessage response = httpClient.PostAsync("/api/eip/record/upload", content).Result;
                    response.EnsureSuccessStatusCode();
                    responseContent = response.Content.ReadAsStringAsync().Result;
                }

                // 判断返回值
                MtResponse mtResponse = JsonConvert.DeserializeObject<MtResponse>(responseContent);
                return new MtResult
                {
                    Success = mtResponse.code.Equals("00000"),
                    Result = responseContent
                };
            }
            catch (Exception ex)
            {
                return new MtResult
                {
                    Success = true,
                    Result = ex.ToString()
                };
            }
        }

        /// <summary>
        /// 使数据失效
        /// </summary>
        public static MtResult Invalidate(EnumTemplate templateId, string batchId)
        {
            try
            {
                // 构建请求体
                InvalidateDetail detail = new InvalidateDetail
                {
                    templateId = templateId,
                    bizLineCode = BizLineCode,
                    supplierCode = SupplierCode,
                    factoryCode = FactoryCode,
                    batchId = batchId,
                    data = new List<string>()
                };

                // 计算Content-MD5
                string requestBody = JsonConvert.SerializeObject(detail);
                string contentMd5 = GetMd5(requestBody);

                // 构造签名字符串计算sha256签名
                string httpMethod = "POST";
                string urlPath = "/api/eip/record/invalidate";
                string stringToSign = $"{httpMethod}\n{contentMd5}\n{urlPath}";
                string sign = GetSha256(stringToSign);

                // 构建提交
                string responseContent;
                using (HttpClient httpClient = new HttpClient())
                {
                    httpClient.BaseAddress = new Uri(Host);
                    httpClient.Timeout = TimeSpan.FromSeconds(10);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Signature", sign);
                    httpClient.DefaultRequestHeaders.Add("S-Ca-Timestamp", DateTimeOffset.Now.ToUnixTimeMilliseconds().ToString());
                    httpClient.DefaultRequestHeaders.Add("S-Ca-App", SCaApp);
                    StringContent content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                    content.Headers.Add("Content-MD5", contentMd5);

                    // 同步调用（网络不好时会阻塞线程）
                    HttpResponseMessage response = httpClient.PostAsync("/api/eip/record/invalidate", content).Result;
                    response.EnsureSuccessStatusCode();
                    responseContent = response.Content.ReadAsStringAsync().Result;
                }

                // 判断返回值
                MtResponse mtResponse = JsonConvert.DeserializeObject<MtResponse>(responseContent);
                return new MtResult
                {
                    Success = mtResponse.code.Equals("00000"),
                    Result = responseContent
                };
            }
            catch (Exception ex)
            {
                return new MtResult
                {
                    Success = true,
                    Result = ex.ToString()
                };
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取MD5
        /// </summary>
        /// <param name="requestBody"></param>
        /// <returns></returns>
        private static string GetMd5(string requestBody)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(requestBody);
            using (MD5 md5 = new MD5CryptoServiceProvider())
            {
                byte[] hashBytes = md5.ComputeHash(bytes);
                return Convert.ToBase64String(hashBytes);
            }
        }

        private static string GetSha256(string stringToSign)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(Secret);
            using (HMACSHA256 hmac = new HMACSHA256(keyBytes))
            {
                byte[] messageBytes = Encoding.UTF8.GetBytes(stringToSign);
                byte[] hashBytes = hmac.ComputeHash(messageBytes);
                return Convert.ToBase64String(hashBytes);
            }
        }

        #endregion
    }
}