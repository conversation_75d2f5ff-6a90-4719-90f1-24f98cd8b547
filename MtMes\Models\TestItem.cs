﻿namespace MtMes.Models
{
    public class TestItem
    {
        /// <summary>
        /// 品类,必填,需严格按照品类文档填写
        /// </summary>
        public string CATEGORY { get; set; }

        /// <summary>
        /// SN,必填,产品SN
        /// </summary>
        public string SN { get; set; }

        /// <summary>
        /// 管理站别,必填
        /// </summary>
        public string MANAGEMENT_STATION { get; set; }

        /// <summary>
        /// 检测值,可选
        /// </summary>
        public string INSPECTION_VALUE { get; set; } = string.Empty;

        /// <summary>
        /// 测试时间,可选
        /// </summary>
        public string INSPECTION_DATE { get; set; }= string.Empty;

        /// <summary>
        /// 测试压力,可选
        /// </summary>
        public string TEST_PRESSURE { get; set; }= string.Empty;

        /// <summary>
        /// 泄露值,可选
        /// </summary>
        public string LEAKED_VALUE { get; set; }= string.Empty;

        /// <summary>
        /// 测试日志,可选
        /// </summary>
        public string TEST_LOG { get; set; }= string.Empty;
    }
}